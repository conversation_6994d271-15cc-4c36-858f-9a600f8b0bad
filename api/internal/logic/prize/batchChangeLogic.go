package prize

import (
	"context"

	"engine/api/internal/svc"
	"engine/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type BatchChangeLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewBatchChangeLogic(ctx context.Context, svcCtx *svc.ServiceContext) *BatchChangeLogic {
	return &BatchChangeLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *BatchChangeLogic) BatchChange(req *types.BatchChangeReq) error {
	// todo: add your logic here and delete this line

	return nil
}
