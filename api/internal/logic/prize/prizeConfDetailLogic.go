package prize

import (
	"context"

	"engine/api/internal/svc"
	"engine/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type PrizeConfDetailLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewPrizeConfDetailLogic(ctx context.Context, svcCtx *svc.ServiceContext) *PrizeConfDetailLogic {
	return &PrizeConfDetailLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *PrizeConfDetailLogic) PrizeConfDetail(req *types.PrizeConfDetailReq) (resp *types.PrizeConfDetailResp, err error) {
	// todo: add your logic here and delete this line

	return
}
