syntax = "v1"

info(
    title: "抽奖"
    author: "gangh"
    email: "<EMAIL>"
    version: "v1"
)

type (
    PrizeRecordListReq {
        Paging
        Keyword string `form:"keyword,optional"`
        Status int64 `form:"status,optional"`
        OrderColumn string `form:"order_column,optional"`
        OrderBy string `form:"order_by,optional"`
        PrizeStartTime string `form:"prize_start_time,optional"`
        PrizeEndTime string `form:"prize_end_time,optional"`
        RedemptionStartTime string `form:"redemption_start_time,optional"`
        RedemptionEndTime string `form:"redemption_end_time,optional"`
    }
    PrizeRecordListResp {
        List []PrizeRecordListInfo `json:"list"`
        Total int64 `json:"total"`                     //总数
        RedemptionCount int64 `json:"redemption_count"`//已核销数
        UnverifiedCount int64 `json:"unverified_count"`//未核销数
    }

    PrizeRecordListInfo {
        Id uint64 `json:"id"`
        Phone string `json:"phone"`                                   // 手机号
        ShortCode string `json:"short_code"`                          // 简码
        GoosName string `json:"goos_name"`                            // 商品名称
        PrizeName string `json:"prize_name"`                          // 奖品名
        PrizeTime string `json:"prize_time"`                          // 中奖时间
        Status uint64 `json:"status"`                                 // 状态(1未核销，2已核销，3已过期)
        CompanyName string `json:"company_name"`                      // 核销公司名称
        RedemptionTime string `json:"redemption_time"`                // 核销时间
    }

    BatchListReq {
        Paging
    }
    BatchListResp {
        List []BatchInfo `json:"list"`
        Total int64 `json:"total"`
    }

    BatchInfo {
        Id uint64 `json:"id"`
        Title string `json:"title"`            // 批次名称
        ShortCode string `json:"short_code"`   // 简码
        GoodsName string `json:"goods_name"`   // 产品名称
        Desc string `json:"desc"`              // 描述
        Status uint64 `json:"status"`          // 1启用，2禁用
        CreateTime string `json:"create_time"` // 创建时间
        StartTime string `json:"start_time"`   // 开始时间
        EndTime string `json:"end_time"`       // 结束时间
    }

    BatchAddReq {
        Title string `json:"title" validate:"max=50" v:"批次名称"`
        ShortCode string `json:"short_code" validate:"max=20" v:"简码"`
        GoodsName string `json:"goods_name" validate:"max=50" v:"产品名称"`
        Desc string `json:"desc,optional" validate:"max=250" v:"描述"`
        StartTime string `json:"start_time" validate:"required" v:"开始时间"`
        EndTime string `json:"end_time" validate:"required" v:"结束时间"`
    }

    BatchChangeReq {
        //是否启用
        IsEnable bool `json:"is_enable" validate:"required" v:"是否启用"`
    }

    PrizeConfDetailReq {
        IdJU
    }
    PrizeConfDetailResp {
        TotalCt uint64 `json:"total_ct"`                              //配置总中奖瓶数汇总
        WinCt uint64 `json:"win_ct"`                                  //已中奖瓶数汇总
        RemainCt uint64 `json:"remain_ct"`                            //总剩余瓶数汇总
        RegionConfList []PrizeRegionConfInfo `json:"region_conf_list"`//大区中奖配置
    }
    PrizeRegionConfInfo {
        RegionName string `json:"region_name"`                           //大区名称
        TotalCt uint64 `json:"total_ct"`                                 //配置总中奖瓶数汇总
        WinCt uint64 `json:"win_ct"`                                     //已中奖瓶数汇总
        RemainCt uint64 `json:"remain_ct"`                               //总剩余瓶数汇总
        RedeemCt uint64 `json:"redeem_ct"`                               //真实总中奖瓶数汇总
        PrizeList []PrizeInfo `json:"prize_list"`                        //奖品列表
    }
    PrizeInfo {
        Id uint64 `json:"id"`
        BatchId uint64 `json:"batch_id"`               // 批次id
        RegionCode string `json:"region_code"`         // 大区code
        RegionName string `json:"region_name"`         // 大区名称
        PrizeName string `json:"prize_name"`           // 奖品名
        Status uint64 `json:"status"`                  // 状态1启用，2禁用
        Points uint64 `json:"points"`                  // 积分（单位分，中奖一次可以兑换多少分）
        RedeemQuantity uint64 `json:"redeem_quantity"` // 总中奖瓶数(单位瓶)
        PrizeCt uint64 `json:"prize_ct"`               // 总中奖次数
        WinCt uint64 `json:"win_ct"`                   // 已中奖次数
        ValidDays uint64 `json:"valid_days"`           // 有效天数(多少天后过期)
    }

    PrizeConfAddReq {
        BatchId uint64 `json:"batch_id" validate:"required" v:"批次id"`
        RegionCode string `json:"region_code" validate:"required" v:"大区code"`
        PrizeName string `json:"prize_name" validate:"required" v:"奖品名"`
        Status uint64 `json:"status" validate:"required" v:"状态1启用，2禁用"`
        Points uint64 `json:"points" validate:"required" v:"积分（单位分，中奖一次可以兑换多少分）"`
        RedeemQuantity uint64 `json:"redeem_quantity" validate:"required" v:"总中奖瓶数(单位瓶)"`
        PrizeCt uint64 `json:"prize_ct" validate:"required" v:"总中奖次数"`
        ValidDays uint64 `json:"valid_days" validate:"required" v:"有效天数(多少天后过期)"`
    }
    PrizeConfUpdareReq {
        IdJU
        PrizeName string `json:"prize_name" validate:"required" v:"奖品名"`
        Status uint64 `json:"status" validate:"required" v:"状态1启用，2禁用"`
        Points uint64 `json:"points" validate:"required" v:"积分（单位分，中奖一次可以兑换多少分）"`
        RedeemQuantity uint64 `json:"redeem_quantity" validate:"required" v:"总中奖瓶数(单位瓶)"`
        PrizeCt uint64 `json:"prize_ct" validate:"required" v:"总中奖次数"`
        ValidDays uint64 `json:"valid_days" validate:"required" v:"有效天数(多少天后过期)"`
    }
)

//后台接口
@server(
    middleware: Global,Admin
    group: prize
    prefix: /mulandoRedeem/v1/prize/admin
    timeout: 3s
)

service mulandoRedeem {
    @handler PrizeRecordList //中奖记录
    get /prizeRecordList (PrizeRecordListReq) returns (PrizeRecordListResp)

    @handler BatchList //批次列表
    get /batchList (BatchListReq) returns (BatchListResp)

    @handler BatchAdd//添加批次
    post /batchAdd (BatchAddReq)

    @handler BatchChange//禁用/启用批次
    post /batchChange (BatchChangeReq)

    @handler PrizeConfDetail //中奖配置
    get /prizeConfDetail (PrizeConfDetailReq) returns (PrizeConfDetailResp)

    @handler PrizeConfAdd   //添加奖品
    post /prizeConfAdd (PrizeConfAddReq)

    @handler PrizeConfUpdare   //修改奖品
    post /prizeConfUpdare (PrizeConfUpdareReq)
}