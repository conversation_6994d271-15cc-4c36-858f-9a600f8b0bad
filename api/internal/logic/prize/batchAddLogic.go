package prize

import (
	"context"

	"engine/api/internal/svc"
	"engine/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type BatchAddLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewBatchAddLogic(ctx context.Context, svcCtx *svc.ServiceContext) *BatchAddLogic {
	return &BatchAddLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *BatchAddLogic) BatchAdd(req *types.BatchAddReq) error {
	// todo: add your logic here and delete this line

	return nil
}
