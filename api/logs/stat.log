2025-08-05T14:46:26.874+08:00	[32;1m stat [0;22m	CPU: 0m, MEMORY: Alloc=2.0Mi, TotalAlloc=2.6Mi, Sys=14.4Mi, NumGC=1	caller=stat/usage.go:61
2025-08-05T14:46:27.229+08:00	[32;1m stat [0;22m	(api) shedding_stat [1m], cpu: 0, total: 1, pass: 1, drop: 0	caller=load/sheddingstat.go:61
2025-08-05T14:46:51.460+08:00	[32;1m stat [0;22m	(go-mulando-redeem) - qps: 0.1/s, drops: 0, avg time: 148.0ms, med: 444.8ms, 90th: 444.8ms, 99th: 444.8ms, 99.9th: 444.8ms	caller=stat/metrics.go:210
2025-08-05T14:47:26.872+08:00	[32;1m stat [0;22m	CPU: 0m, MEMORY: Alloc=2.3Mi, TotalAlloc=2.9Mi, Sys=14.4Mi, NumGC=1	caller=stat/usage.go:61
2025-08-05T14:47:27.227+08:00	[32;1m stat [0;22m	(api) shedding_stat [1m], cpu: 0, total: 2, pass: 2, drop: 0	caller=load/sheddingstat.go:61
2025-08-05T14:47:51.459+08:00	[32;1m stat [0;22m	(go-mulando-redeem) - qps: 0.0/s, drops: 0, avg time: 0.0ms, med: 0.0ms, 90th: 0.0ms, 99th: 0.0ms, 99.9th: 0.0ms	caller=stat/metrics.go:210
2025-08-05T14:48:26.871+08:00	[32;1m stat [0;22m	CPU: 0m, MEMORY: Alloc=2.0Mi, TotalAlloc=3.2Mi, Sys=14.9Mi, NumGC=2	caller=stat/usage.go:61
2025-08-05T14:48:27.226+08:00	[32;1m stat [0;22m	(api) shedding_stat [1m], cpu: 0, total: 1, pass: 1, drop: 0	caller=load/sheddingstat.go:61
2025-08-05T14:48:46.728+08:00	[32;1m stat [0;22m	(go-mulando-redeem) - qps: 0.0/s, drops: 0, avg time: 405.0ms, med: 406.0ms, 90th: 406.0ms, 99th: 406.0ms, 99.9th: 406.0ms	caller=stat/metrics.go:210
2025-08-05T14:56:59.278+08:00	[32;1m stat [0;22m	CPU: 0m, MEMORY: Alloc=2.2Mi, TotalAlloc=2.7Mi, Sys=14.1Mi, NumGC=1	caller=stat/usage.go:61
2025-08-05T14:56:59.624+08:00	[32;1m stat [0;22m	(api) shedding_stat [1m], cpu: 0, total: 4, pass: 4, drop: 0	caller=load/sheddingstat.go:61
2025-08-05T14:57:00.250+08:00	[32;1m stat [0;22m	(go-mulando-redeem) - qps: 0.1/s, drops: 0, avg time: 0.0ms, med: 0.2ms, 90th: 0.2ms, 99th: 0.2ms, 99.9th: 0.2ms	caller=stat/metrics.go:210
2025-08-05T14:57:59.275+08:00	[32;1m stat [0;22m	CPU: 0m, MEMORY: Alloc=2.4Mi, TotalAlloc=2.9Mi, Sys=14.1Mi, NumGC=1	caller=stat/usage.go:61
2025-08-05T14:57:59.622+08:00	[32;1m stat [0;22m	(api) shedding_stat [1m], cpu: 0, total: 0, pass: 0, drop: 0	caller=load/sheddingstat.go:61
2025-08-05T14:58:00.248+08:00	[32;1m stat [0;22m	(go-mulando-redeem) - qps: 0.0/s, drops: 0, avg time: 0.0ms, med: 0.0ms, 90th: 0.0ms, 99th: 0.0ms, 99.9th: 0.0ms	caller=stat/metrics.go:210
2025-08-05T14:59:26.476+08:00	[32;1m stat [0;22m	CPU: 0m, MEMORY: Alloc=2.0Mi, TotalAlloc=2.6Mi, Sys=14.4Mi, NumGC=1	caller=stat/usage.go:61
2025-08-05T14:59:26.798+08:00	[32;1m stat [0;22m	(api) shedding_stat [1m], cpu: 0, total: 1, pass: 1, drop: 0	caller=load/sheddingstat.go:61
2025-08-05T14:59:34.349+08:00	[32;1m stat [0;22m	(go-mulando-redeem) - qps: 0.0/s, drops: 0, avg time: 0.0ms, med: 0.2ms, 90th: 0.2ms, 99th: 0.2ms, 99.9th: 0.2ms	caller=stat/metrics.go:210
2025-08-05T15:02:18.288+08:00	[32;1m stat [0;22m	CPU: 0m, MEMORY: Alloc=2.0Mi, TotalAlloc=2.6Mi, Sys=14.1Mi, NumGC=1	caller=stat/usage.go:61
2025-08-05T15:02:18.585+08:00	[32;1m stat [0;22m	(api) shedding_stat [1m], cpu: 0, total: 1, pass: 1, drop: 0	caller=load/sheddingstat.go:61
2025-08-05T15:02:19.450+08:00	[32;1m stat [0;22m	(go-mulando-redeem) - qps: 0.0/s, drops: 0, avg time: 0.0ms, med: 0.4ms, 90th: 0.4ms, 99th: 0.4ms, 99.9th: 0.4ms	caller=stat/metrics.go:210
2025-08-05T15:05:36.982+08:00	[32;1m stat [0;22m	CPU: 0m, MEMORY: Alloc=2.0Mi, TotalAlloc=2.6Mi, Sys=14.4Mi, NumGC=1	caller=stat/usage.go:61
2025-08-05T15:05:37.282+08:00	[32;1m stat [0;22m	(api) shedding_stat [1m], cpu: 0, total: 1, pass: 1, drop: 0	caller=load/sheddingstat.go:61
2025-08-05T15:05:39.328+08:00	[32;1m stat [0;22m	(go-mulando-redeem) - qps: 0.0/s, drops: 0, avg time: 1.0ms, med: 1.3ms, 90th: 1.3ms, 99th: 1.3ms, 99.9th: 1.3ms	caller=stat/metrics.go:210
2025-08-05T15:06:36.980+08:00	[32;1m stat [0;22m	CPU: 0m, MEMORY: Alloc=2.2Mi, TotalAlloc=2.8Mi, Sys=14.4Mi, NumGC=1	caller=stat/usage.go:61
2025-08-05T15:06:37.280+08:00	[32;1m stat [0;22m	(api) shedding_stat [1m], cpu: 0, total: 0, pass: 0, drop: 0	caller=load/sheddingstat.go:61
2025-08-05T15:06:39.326+08:00	[32;1m stat [0;22m	(go-mulando-redeem) - qps: 0.0/s, drops: 0, avg time: 0.0ms, med: 0.0ms, 90th: 0.0ms, 99th: 0.0ms, 99.9th: 0.0ms	caller=stat/metrics.go:210
2025-08-05T15:07:36.980+08:00	[32;1m stat [0;22m	CPU: 0m, MEMORY: Alloc=1.9Mi, TotalAlloc=3.0Mi, Sys=14.9Mi, NumGC=2	caller=stat/usage.go:61
2025-08-05T15:07:37.279+08:00	[32;1m stat [0;22m	(api) shedding_stat [1m], cpu: 0, total: 0, pass: 0, drop: 0	caller=load/sheddingstat.go:61
2025-08-05T15:07:39.325+08:00	[32;1m stat [0;22m	(go-mulando-redeem) - qps: 0.0/s, drops: 0, avg time: 0.0ms, med: 0.0ms, 90th: 0.0ms, 99th: 0.0ms, 99.9th: 0.0ms	caller=stat/metrics.go:210
2025-08-05T15:08:36.978+08:00	[32;1m stat [0;22m	CPU: 0m, MEMORY: Alloc=2.0Mi, TotalAlloc=3.2Mi, Sys=14.9Mi, NumGC=2	caller=stat/usage.go:61
2025-08-05T15:08:37.278+08:00	[32;1m stat [0;22m	(api) shedding_stat [1m], cpu: 0, total: 0, pass: 0, drop: 0	caller=load/sheddingstat.go:61
2025-08-05T15:08:39.323+08:00	[32;1m stat [0;22m	(go-mulando-redeem) - qps: 0.0/s, drops: 0, avg time: 0.0ms, med: 0.0ms, 90th: 0.0ms, 99th: 0.0ms, 99.9th: 0.0ms	caller=stat/metrics.go:210
2025-08-05T15:09:36.977+08:00	[32;1m stat [0;22m	CPU: 0m, MEMORY: Alloc=1.8Mi, TotalAlloc=3.4Mi, Sys=14.9Mi, NumGC=3	caller=stat/usage.go:61
2025-08-05T15:09:37.277+08:00	[32;1m stat [0;22m	(api) shedding_stat [1m], cpu: 0, total: 0, pass: 0, drop: 0	caller=load/sheddingstat.go:61
2025-08-05T15:09:39.322+08:00	[32;1m stat [0;22m	(go-mulando-redeem) - qps: 0.0/s, drops: 0, avg time: 0.0ms, med: 0.0ms, 90th: 0.0ms, 99th: 0.0ms, 99.9th: 0.0ms	caller=stat/metrics.go:210
2025-08-05T15:11:39.523+08:00	[32;1m stat [0;22m	CPU: 0m, MEMORY: Alloc=2.1Mi, TotalAlloc=2.7Mi, Sys=14.4Mi, NumGC=1	caller=stat/usage.go:61
2025-08-05T15:11:39.834+08:00	[32;1m stat [0;22m	(api) shedding_stat [1m], cpu: 0, total: 3, pass: 3, drop: 0	caller=load/sheddingstat.go:61
2025-08-05T15:11:41.098+08:00	[32;1m stat [0;22m	(go-mulando-redeem) - qps: 0.1/s, drops: 0, avg time: 212.3ms, med: 294.7ms, 90th: 294.7ms, 99th: 294.7ms, 99.9th: 294.7ms	caller=stat/metrics.go:210
2025-08-05T15:36:32.739+08:00	[32;1m stat [0;22m	CPU: 0m, MEMORY: Alloc=2.1Mi, TotalAlloc=2.6Mi, Sys=14.4Mi, NumGC=1	caller=stat/usage.go:61
2025-08-05T15:36:33.125+08:00	[32;1m stat [0;22m	(api) shedding_stat [1m], cpu: 0, total: 1, pass: 1, drop: 0	caller=load/sheddingstat.go:61
2025-08-05T15:38:31.037+08:00	[32;1m stat [0;22m	CPU: 0m, MEMORY: Alloc=2.2Mi, TotalAlloc=2.8Mi, Sys=14.4Mi, NumGC=1	caller=stat/usage.go:61
2025-08-05T15:38:31.367+08:00	[32;1m stat [0;22m	(api) shedding_stat [1m], cpu: 0, total: 3, pass: 3, drop: 0	caller=load/sheddingstat.go:61
2025-08-05T15:38:34.806+08:00	[32;1m stat [0;22m	(go-mulando-redeem) - qps: 0.1/s, drops: 0, avg time: 202.7ms, med: 405.2ms, 90th: 405.2ms, 99th: 405.2ms, 99.9th: 405.2ms	caller=stat/metrics.go:210
