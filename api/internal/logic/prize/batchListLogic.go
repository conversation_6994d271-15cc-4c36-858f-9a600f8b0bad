package prize

import (
	"context"

	"engine/api/internal/svc"
	"engine/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type BatchListLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewBatchListLogic(ctx context.Context, svcCtx *svc.ServiceContext) *BatchListLogic {
	return &BatchListLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *BatchListLogic) BatchList(req *types.BatchListReq) (resp *types.BatchListResp, err error) {
	// todo: add your logic here and delete this line

	return
}
